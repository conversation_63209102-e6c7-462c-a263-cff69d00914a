{"api_credentials": {"api_key": "BjxCyX7C", "secret_key": "4afa023f-72e9-4872-839b-f8e9f30884a9", "username": "AAAM575832", "password": "1003", "totp_secret": "W3SHWDX4JUB775UH5UIAOLUZCQ", "jwt_token": "Bearer eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WU6rEDt1TAOd-CMIk0KEco-PJlBgGb2rV4OLv3tkxTuNiynAHLrBYtwXBAYlrdKM5wW67R0IrhO2bIs3uOd-Vg", "refresh_token": "eyJhbGciOiJIUzUxMiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UPsWiXjMieUI65l8r_R8Wii88xgen1nOHamAsy6Yye8LxAuAE6hw_ZVrxXSrdNcMYDNCmm-y3vwhrsPh-PCTeA", "feed_token": "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6IkFBQU01NzU4MzIiLCJpYXQiOjE3NDgyNTQxMzIsImV4cCI6MTc0ODM0MDUzMn0.ssZFUW8tKPlcU7UhUg09EYdkVjZhEKSJQfVN3mZxeoaPiFrOXA844oF_oZHH5OGOgEjObhnWOQBmoEOMYtisxg"}, "api_settings": {"paper_trading": true, "live_trading": false, "base_url": "https://apiconnect.angelone.in", "websocket_url": "wss://smartapisocket.angelone.in/smart-stream", "api_timeout": 30, "retry_attempts": 3, "rate_limit_per_second": 10, "rate_limit_per_minute": 600, "rate_limit_per_day": 50000}, "trading_settings": {"variety": "NORMAL", "exchange": "NSE", "order_type": "LIMIT", "product_type": "INTRADAY", "duration": "DAY", "capital_per_symbol": 100000, "profit_target_percent": 1.2, "stop_loss_percent": 0.5, "max_positions_per_symbol": 1}, "data_settings": {"historical_interval": "FIFTEEN_MINUTE", "realtime_interval": "FIFTEEN_MINUTE", "historical_days": 730, "exchanges": ["NSE", "BSE"], "data_format": "json", "websocket_enabled": true, "backup_enabled": true}, "websocket_settings": {"mode": 1, "exchange_type": 1, "correlation_id": "trading_bot_001", "auto_reconnect": true, "heartbeat_interval": 30}, "security": {"encrypt_credentials": false, "log_api_calls": true, "mask_sensitive_data": true, "session_timeout": 3600}, "last_updated": "2025-01-15T00:00:00Z", "setup_instructions": ["1. Get API Key from https://smartapi.angelbroking.com/", "2. Username = Your AngelOne Client Code (e.g., A12345)", "3. Password = Your AngelOne PIN (not login password)", "4. TOTP Secret = Get from QR code when enabling TOTP", "5. Install: pip install smartapi-python pyotp", "6. Test with paper_trading: true first"], "real_api_limits": {"note": "ACTUAL AngelOne SmartAPI Rate Limits", "order_placement": "10 requests per second", "order_modification": "10 requests per second", "market_data": "10 requests per second", "historical_data": "3 requests per second", "portfolio_data": "1 request per second", "daily_limit": "No specific daily limit mentioned", "websocket_connections": "5 concurrent connections max"}}