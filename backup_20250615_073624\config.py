"""
Configuration management for Angel One trading data fetcher
"""
import json
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any

class Config:
    def __init__(self, config_file: str = "angelone_smartapi_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file {self.config_file} not found")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in configuration file {self.config_file}")
    
    @property
    def api_credentials(self) -> Dict[str, str]:
        """Get API credentials"""
        return self.config.get("api_credentials", {})
    
    @property
    def api_settings(self) -> Dict[str, Any]:
        """Get API settings"""
        return self.config.get("api_settings", {})
    
    @property
    def data_settings(self) -> Dict[str, Any]:
        """Get data settings"""
        return self.config.get("data_settings", {})
    
    @property
    def trading_settings(self) -> Dict[str, Any]:
        """Get trading settings"""
        return self.config.get("trading_settings", {})

# Database configuration
DATABASE_CONFIG = {
    "sqlite": {
        "url": "sqlite:///Data/trading_data.db",
        "echo": False
    },
    "mysql": {
        "url": "mysql+pymysql://user:password@localhost/trading_db",
        "echo": False
    },
    "postgresql": {
        "url": "postgresql://user:password@localhost/trading_db",
        "echo": False
    }
}

# File paths
PATHS = {
    "excel_file": "paper_trade.xlsx",
    "tokens_file": "Data/angelone_tokens.txt",
    "csv_output_dir": "Data/csv_files",
    "logs_dir": "logs"
}

# Data fetching settings
DATA_FETCH_CONFIG = {
    "interval": "FIFTEEN_MINUTE",  # 15-minute intervals
    "exchange": "NSE",
    "days_back": 1,  # Last 1 trading day
    "batch_size": 10,  # Process symbols in batches
    "delay_between_requests": 0.1,  # Delay in seconds to respect rate limits
    "max_retries": 3,
    "timeout": 30
}

# Trading hours (IST)
TRADING_HOURS = {
    "market_open": "09:15",
    "market_close": "15:30",
    "pre_market_open": "09:00",
    "post_market_close": "16:00"
}

def get_last_trading_day() -> datetime:
    """Get the last trading day (excluding weekends)"""
    today = datetime.now()

    # If today is Monday, last trading day was Friday
    if today.weekday() == 0:  # Monday
        return today - timedelta(days=3)
    # If today is Sunday, last trading day was Friday
    elif today.weekday() == 6:  # Sunday
        return today - timedelta(days=2)
    # Otherwise, last trading day was yesterday
    else:
        return today - timedelta(days=1)

def get_current_trading_day() -> datetime:
    """Get the current trading day (today if it's a weekday, last trading day if weekend)"""
    today = datetime.now()

    # If today is Saturday, return Friday
    if today.weekday() == 5:  # Saturday
        return today - timedelta(days=1)
    # If today is Sunday, return Friday
    elif today.weekday() == 6:  # Sunday
        return today - timedelta(days=2)
    # Otherwise, return today
    else:
        return today

def ensure_directories():
    """Ensure required directories exist"""
    for path in [PATHS["csv_output_dir"], PATHS["logs_dir"]]:
        os.makedirs(path, exist_ok=True)
