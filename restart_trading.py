#!/usr/bin/env python3
"""
Trading Bot Restart Script
==========================

This script completely cleans all trading data and resets the system for a fresh start.

What it cleans:
- DB1 (trading_data.db): Market data, signals, patterns
- DB2 (trading_operations.db): Active positions, paper trading records
- CSV files: Paper trading records
- Excel files: Paper trading data
- Log files: All historical logs
- Cache files: Python cache and temporary files

Author: Trading Bot System
Date: 2025-06-15
"""

import os
import sqlite3
import shutil
import logging
import sys
from datetime import datetime
from pathlib import Path

class TradingSystemRestart:
    def __init__(self):
        self.setup_logging()
        self.db1_path = 'Data/trading_data.db'
        self.db2_path = 'Data/trading_operations.db'
        self.db2_data_path = 'Data/db2_trading_data.db'
        self.csv_file = 'Data/csv_files/paper_trade.csv'
        self.excel_file = 'paper_trade.xlsx'
        self.logs_dir = 'logs'
        self.cache_dir = '__pycache__'
        
    def setup_logging(self):
        """Setup logging for the restart process"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'restart_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)

    def confirm_restart(self):
        """Ask user for confirmation before proceeding"""
        print("\n" + "="*60)
        print("🚨 TRADING SYSTEM COMPLETE RESTART 🚨")
        print("="*60)
        print("\nThis will permanently delete ALL trading data:")
        print("✗ All active positions")
        print("✗ All paper trading records")
        print("✗ All market data and signals")
        print("✗ All profit/loss history")
        print("✗ All pattern detection data")
        print("✗ All log files")
        print("✗ All CSV and Excel trading files")
        print("\n⚠️  THIS ACTION CANNOT BE UNDONE! ⚠️")
        
        response = input("\nType 'RESTART' to confirm complete system reset: ")
        if response != 'RESTART':
            print("❌ Restart cancelled. No changes made.")
            return False
        return True

    def backup_config_files(self):
        """Backup important configuration files before cleanup"""
        try:
            backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(backup_dir, exist_ok=True)
            
            config_files = [
                'config.py',
                'angelone_smartapi_config.json',
                'Data/angelone_tokens.txt'
            ]
            
            for file_path in config_files:
                if os.path.exists(file_path):
                    shutil.copy2(file_path, backup_dir)
                    self.logger.info(f"✅ Backed up: {file_path}")
            
            self.logger.info(f"📁 Configuration backup created: {backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error creating backup: {e}")
            return False

    def clean_database_files(self):
        """Remove all database files"""
        try:
            db_files = [
                self.db1_path,
                self.db2_path, 
                self.db2_data_path,
                f"{self.db1_path}-shm",
                f"{self.db1_path}-wal",
                f"{self.db2_path}-shm", 
                f"{self.db2_path}-wal"
            ]
            
            for db_file in db_files:
                if os.path.exists(db_file):
                    os.remove(db_file)
                    self.logger.info(f"🗑️  Deleted database: {db_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error cleaning databases: {e}")
            return False

    def clean_data_files(self):
        """Remove CSV and Excel data files"""
        try:
            # Remove CSV files
            if os.path.exists(self.csv_file):
                os.remove(self.csv_file)
                self.logger.info(f"🗑️  Deleted CSV file: {self.csv_file}")
            
            # Remove Excel file
            if os.path.exists(self.excel_file):
                os.remove(self.excel_file)
                self.logger.info(f"🗑️  Deleted Excel file: {self.excel_file}")
            
            # Clean entire csv_files directory
            csv_dir = 'Data/csv_files'
            if os.path.exists(csv_dir):
                shutil.rmtree(csv_dir)
                self.logger.info(f"🗑️  Deleted directory: {csv_dir}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error cleaning data files: {e}")
            return False

    def clean_log_files(self):
        """Remove all log files"""
        try:
            if os.path.exists(self.logs_dir):
                shutil.rmtree(self.logs_dir)
                self.logger.info(f"🗑️  Deleted logs directory: {self.logs_dir}")
            
            # Remove any standalone log files
            for file in os.listdir('.'):
                if file.endswith('.log'):
                    os.remove(file)
                    self.logger.info(f"🗑️  Deleted log file: {file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error cleaning log files: {e}")
            return False

    def clean_cache_files(self):
        """Remove Python cache files"""
        try:
            if os.path.exists(self.cache_dir):
                shutil.rmtree(self.cache_dir)
                self.logger.info(f"🗑️  Deleted cache directory: {self.cache_dir}")
            
            # Remove .pyc files
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.pyc'):
                        file_path = os.path.join(root, file)
                        os.remove(file_path)
                        self.logger.info(f"🗑️  Deleted cache file: {file_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error cleaning cache files: {e}")
            return False

    def recreate_directory_structure(self):
        """Recreate necessary directory structure"""
        try:
            directories = [
                'Data',
                'Data/csv_files',
                'logs'
            ]
            
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
                self.logger.info(f"📁 Created directory: {directory}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error recreating directories: {e}")
            return False

    def initialize_fresh_databases(self):
        """Initialize fresh database files with proper schema"""
        try:
            # Initialize DB1 (trading_data.db)
            self._init_db1()
            
            # Initialize DB2 (trading_operations.db)  
            self._init_db2()
            
            self.logger.info("✅ Fresh databases initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error initializing databases: {e}")
            return False

    def _init_db1(self):
        """Initialize DB1 with basic schema"""
        conn = sqlite3.connect(self.db1_path)
        cursor = conn.cursor()
        
        # Create basic tables for DB1
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            signal_type TEXT NOT NULL,
            price REAL NOT NULL,
            timestamp DATETIME NOT NULL,
            pattern_sequence TEXT,
            source TEXT DEFAULT 'DB1',
            executed BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS rolling_window_patterns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL UNIQUE,
            fr_pattern TEXT,
            price_history TEXT,
            last_update DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()

    def _init_db2(self):
        """Initialize DB2 with basic schema"""
        conn = sqlite3.connect(self.db2_path)
        cursor = conn.cursor()
        
        # Create trading positions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trading_positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            buy_price REAL NOT NULL,
            sell_price REAL,
            shares_quantity INTEGER NOT NULL,
            investment REAL NOT NULL,
            target_value REAL NOT NULL,
            target_price REAL NOT NULL,
            buy_time DATETIME NOT NULL,
            sell_time DATETIME,
            actual_profit REAL DEFAULT 0,
            profit_amount REAL DEFAULT 0,
            status TEXT DEFAULT 'ACTIVE',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create portfolio status table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS portfolio_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            total_pot REAL NOT NULL DEFAULT 22200000.0,
            vault_amount REAL NOT NULL DEFAULT 0.0,
            active_positions INTEGER NOT NULL DEFAULT 0,
            completed_trades INTEGER NOT NULL DEFAULT 0,
            total_profit REAL NOT NULL DEFAULT 0.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()

    def run_restart(self):
        """Execute the complete restart process"""
        print("\n🚀 Starting Trading System Restart...")
        
        if not self.confirm_restart():
            return False
        
        steps = [
            ("Creating configuration backup", self.backup_config_files),
            ("Cleaning database files", self.clean_database_files),
            ("Cleaning data files", self.clean_data_files),
            ("Cleaning log files", self.clean_log_files),
            ("Cleaning cache files", self.clean_cache_files),
            ("Recreating directory structure", self.recreate_directory_structure),
            ("Initializing fresh databases", self.initialize_fresh_databases)
        ]
        
        for step_name, step_function in steps:
            print(f"\n📋 {step_name}...")
            if not step_function():
                print(f"❌ Failed: {step_name}")
                return False
            print(f"✅ Completed: {step_name}")
        
        print("\n" + "="*60)
        print("🎉 TRADING SYSTEM RESTART COMPLETED SUCCESSFULLY! 🎉")
        print("="*60)
        print("\n✅ All data has been cleaned")
        print("✅ Fresh databases initialized")
        print("✅ Directory structure recreated")
        print("✅ Configuration files backed up")
        print("\n🚀 Your trading system is ready for a fresh start!")
        print("\nNext steps:")
        print("1. Start your trading bot components")
        print("2. Verify all systems are working")
        print("3. Begin fresh trading operations")
        
        return True

def main():
    """Main function to run the restart process"""
    restart_system = TradingSystemRestart()
    success = restart_system.run_restart()
    
    if success:
        print(f"\n📝 Restart log saved for reference")
        sys.exit(0)
    else:
        print(f"\n❌ Restart process failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
